# == Route Map
#
#                                   Prefix Verb URI Pattern                                                                                       Controller#Action
#                                               /assets                                                                                           Propshaft::Server
#                                    about GET  /about(.:format)                                                                                  pages#about
#                       rails_health_check GET  /up(.:format)                                                                                     rails/health#show
#         turbo_recede_historical_location GET  /recede_historical_location(.:format)                                                             turbo/native/navigation#recede
#         turbo_resume_historical_location GET  /resume_historical_location(.:format)                                                             turbo/native/navigation#resume
#        turbo_refresh_historical_location GET  /refresh_historical_location(.:format)                                                            turbo/native/navigation#refresh
#            rails_postmark_inbound_emails POST /rails/action_mailbox/postmark/inbound_emails(.:format)                                           action_mailbox/ingresses/postmark/inbound_emails#create
#               rails_relay_inbound_emails POST /rails/action_mailbox/relay/inbound_emails(.:format)                                              action_mailbox/ingresses/relay/inbound_emails#create
#            rails_sendgrid_inbound_emails POST /rails/action_mailbox/sendgrid/inbound_emails(.:format)                                           action_mailbox/ingresses/sendgrid/inbound_emails#create
#      rails_mandrill_inbound_health_check GET  /rails/action_mailbox/mandrill/inbound_emails(.:format)                                           action_mailbox/ingresses/mandrill/inbound_emails#health_check
#            rails_mandrill_inbound_emails POST /rails/action_mailbox/mandrill/inbound_emails(.:format)                                           action_mailbox/ingresses/mandrill/inbound_emails#create
#             rails_mailgun_inbound_emails POST /rails/action_mailbox/mailgun/inbound_emails/mime(.:format)                                       action_mailbox/ingresses/mailgun/inbound_emails#create
#           rails_conductor_inbound_emails GET  /rails/conductor/action_mailbox/inbound_emails(.:format)                                          rails/conductor/action_mailbox/inbound_emails#index
#                                          POST /rails/conductor/action_mailbox/inbound_emails(.:format)                                          rails/conductor/action_mailbox/inbound_emails#create
#        new_rails_conductor_inbound_email GET  /rails/conductor/action_mailbox/inbound_emails/new(.:format)                                      rails/conductor/action_mailbox/inbound_emails#new
#            rails_conductor_inbound_email GET  /rails/conductor/action_mailbox/inbound_emails/:id(.:format)                                      rails/conductor/action_mailbox/inbound_emails#show
# new_rails_conductor_inbound_email_source GET  /rails/conductor/action_mailbox/inbound_emails/sources/new(.:format)                              rails/conductor/action_mailbox/inbound_emails/sources#new
#    rails_conductor_inbound_email_sources POST /rails/conductor/action_mailbox/inbound_emails/sources(.:format)                                  rails/conductor/action_mailbox/inbound_emails/sources#create
#    rails_conductor_inbound_email_reroute POST /rails/conductor/action_mailbox/:inbound_email_id/reroute(.:format)                               rails/conductor/action_mailbox/reroutes#create
# rails_conductor_inbound_email_incinerate POST /rails/conductor/action_mailbox/:inbound_email_id/incinerate(.:format)                            rails/conductor/action_mailbox/incinerates#create
#                       rails_service_blob GET  /rails/active_storage/blobs/redirect/:signed_id/*filename(.:format)                               active_storage/blobs/redirect#show
#                 rails_service_blob_proxy GET  /rails/active_storage/blobs/proxy/:signed_id/*filename(.:format)                                  active_storage/blobs/proxy#show
#                                          GET  /rails/active_storage/blobs/:signed_id/*filename(.:format)                                        active_storage/blobs/redirect#show
#                rails_blob_representation GET  /rails/active_storage/representations/redirect/:signed_blob_id/:variation_key/*filename(.:format) active_storage/representations/redirect#show
#          rails_blob_representation_proxy GET  /rails/active_storage/representations/proxy/:signed_blob_id/:variation_key/*filename(.:format)    active_storage/representations/proxy#show
#                                          GET  /rails/active_storage/representations/:signed_blob_id/:variation_key/*filename(.:format)          active_storage/representations/redirect#show
#                       rails_disk_service GET  /rails/active_storage/disk/:encoded_key/*filename(.:format)                                       active_storage/disk#show
#                update_rails_disk_service PUT  /rails/active_storage/disk/:encoded_token(.:format)                                               active_storage/disk#update
#                     rails_direct_uploads POST /rails/active_storage/direct_uploads(.:format)                                                    active_storage/direct_uploads#create

Rails
  .application
  .routes
  .draw do
    # Mount Pay engine for webhooks and customer portal
    mount Pay::Engine => '/pay', :as => :pay_engine # Changed :as to avoid conflict
    mount MissionControl::Jobs::Engine, at: '/jobs'

    get 'up' => 'rails/health#show', :as => :rails_health_check
    get 'sign_in', to: 'sessions#new'
    post 'sign_in', to: 'sessions#create'
    get 'sign_up', to: 'registrations#new'
    post 'sign_up', to: 'registrations#create'
    delete 'sign_out', to: 'sessions#destroy_current', as: :global_sign_out # Add dedicated sign-out route name
    resources :sessions, only: %i[index show destroy] # Keep for managing specific sessions
    resource :password, only: %i[edit update]
    resource :account_profile,
             only: %i[edit update],
             path: 'account/profile',
             controller: 'account_profiles' # Add account profile route

    namespace :identity do
      resource :email, only: %i[edit update]
      resource :email_verification, only: %i[show create] do
        post :resend, on: :collection # Add route for resending verification
      end
      resource :password_reset, only: %i[new edit create update]
    end

    resources :organizations do
      member { post :switch }
    end

    get 'onboarding/personal', to: 'onboarding#personal'
    get 'onboarding/organization', to: 'onboarding#organization'
    patch 'onboarding/update_personal', to: 'onboarding#update_personal'
    patch 'onboarding/update_organization', to: 'onboarding#update_organization'

    root 'launchpad#index'
    get '/launchpad', to: 'launchpad#index', as: :launchpad # Add launchpad route
    get 'about', to: 'home#about'
    get 'settings', to: 'home#settings'

    # Add these routes within your existing scout namespace
    namespace :scout do
      root 'jobs#index'
      resources :applicants, only: %i[index show update] do
        collection { get :placeholder }
        member do
          get :details
          get :stage_change_form
        end
        resources :notes, only: %i[new create], controller: 'applicant_notes'
      end

      resources :messages, only: [:index]

      resources :jobs do
        member do
          get :duplicate
          get :preview
        end
        resources :applicants, only: [:index], module: :jobs
        resources :conversations, only: [:create]

        # Route for job payment (nested under jobs)
        resource :payment, only: [:create], controller: 'job_payments' do
          collection do
            get :success
            get :cancel
          end
        end
      end

      resources :conversations, only: %i[index show create] do
        resources :messages, only: [:create]
        collection do
          resources :archives, only: %i[index show], module: :conversations
          get 'modal/:applicant_user_id',
              to: 'conversations#show_modal',
              as: :modal
        end
        member do
          post :archive
          post :unarchive
        end
      end

      resources :talent, only: %i[index show] do
        member do
          post 'bookmark', to: 'talent_bookmarks#create'
          delete 'bookmark', to: 'talent_bookmarks#destroy'
        end
        resources :bookmarks,
                  only: %i[create destroy],
                  controller: 'talent_bookmarks',
                  as: :talent_bookmarks
      end
      resource :settings, only: %i[show edit update]
      resources :invitations, only: %i[new create]

      resources :conversations, only: [:create] do
        resources :messages, only: [:create]
      end

      resources :job_invitations, only: [:create]

      # Chat requests
      get 'chat_requests/new', to: 'chat_requests#new', as: :new_chat_request
      post 'chat_requests', to: 'chat_requests#create', as: :create_chat_request
    end

    namespace :talent do
      root 'jobs#index'
      resources :messages, only: [:index]

      resources :jobs, only: %i[index show] do
        member do
          post :save
          delete :unsave
        end
        resources :conversations, only: [:create]
        resources :job_applications, only: %i[create new] do
          collection { post :submit_step }
        end
      end

      resources :job_applications, only: %i[index show update edit] do
        member { post :withdraw }
      end

      resources :conversations, only: %i[index show] do
        member do
          post :archive
          post :unarchive
          post :archive
          post :unarchive
          post :bookmark # Add bookmark route
          post :unbookmark # Add unbookmark route
        end

        collection { resources :archives, module: :conversations }
        resources :messages, only: [:create]
      end

      resource :settings, only: %i[show update]

      namespace :settings do
        resource :passwords, only: %i[show update]
        resource :subscription, only: %i[show] # Add subscription route
      end

      resources :job_invitations, only: %i[index destroy] do
        member do
          post :accept
          post :ignore
        end
      end

      resource :profile, only: %i[show edit update create]

      # Chat requests
      resources :chat_requests, only: [:index] do
        member do
          post :accept
          post :decline
        end
      end

      # Talent subscription routes
      resource :subscription, only: %i[create destroy] do
        # Using destroy for potential cancellation later
        get :success # Add success route
        get :cancel # Add cancel route
      end
    end

    # Super Admin routes
    namespace :super_admin do
      root 'dashboard#index'
      resources :users, only: [:index]
      resources :masquerades, only: [:create] do
        collection { delete :destroy }
      end
    end

    # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
    # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
    # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
  end
