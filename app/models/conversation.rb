# == Schema Information
#
# Table name: conversations
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  job_id     :bigint
#
# Indexes
#
#  index_conversations_on_job_id  (job_id)
#
# Foreign Keys
#
#  fk_rails_...  (job_id => jobs.id) ON DELETE => nullify
#
class Conversation < ApplicationRecord
  searchkick word_start: [ :participant_names, :last_message, :messages ],
             callbacks: :async,
             word_middle: [ :messages ],
             searchable: [ :participant_names, :last_message, :messages ]

  belongs_to :job, optional: true # Allow `job` to be nil

  has_many :conversation_participants
  has_many :users, through: :conversation_participants
  has_many :messages

  scope :active_for, ->(user) {
    where(id: ConversationParticipant
      .where(user: user)
      .where(archived: [ false, nil ])
      .select(:conversation_id))
  }

  scope :archived_for, ->(user) {
    where(id: ConversationParticipant
      .where(user: user, archived: true)
      .select(:conversation_id))
  }

  def self.active_for(user)
    joins(:conversation_participants)
      .where(conversation_participants: { user: user })
      .where(conversation_participants: { archived: [ false, nil ] })
      .distinct
  end

  def self.archived_for(user)
    joins(:conversation_participants)
      .where(conversation_participants: { user: user, archived: true })
      .distinct
  end

  def search_data
    {
      participant_names: users.pluck(:first_name, :last_name).flatten,
      last_message: messages.last&.body,
      messages: messages.pluck(:body).join(" "),
      user_ids: users.pluck(:id),
      updated_at: updated_at,
      archived: conversation_participants.where(user_id: user_ids).pluck(:archived)
    }
  end

  def self.find_or_create_by_participants(user1, user2, job = nil)
    conversation = joins(:conversation_participants)
      .where(job: job)
      .where(conversation_participants: { user_id: [ user1.id, user2.id ] })
      .group("conversations.id")
      .having("COUNT(conversation_participants.id) = 2")
      .first

    unless conversation
      conversation = create(job: job)
      conversation.users << [ user1, user2 ]
    end

    conversation
  end

  def recipients_of(user)
    users.where.not(id: user.id)
  end
end
